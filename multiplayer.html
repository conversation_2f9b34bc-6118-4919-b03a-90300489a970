<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Person Picker - Multiplayer Games</title>

    <!-- Cache Control -->
    <meta http-equiv="Cache-Control" content="public, max-age=31536000">

    <!-- SEO Meta Tags -->
    <meta name="description" content="Play multiplayer games with Person Picker. Connect with friends and colleagues remotely for fun selection games.">
    <meta name="keywords" content="multiplayer games, random name picker, team selection, remote games">
    <meta name="author" content="Person Picker">

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://personpicker.com/multiplayer.html">
    <meta property="og:title" content="Person Picker - Multiplayer Games">
    <meta property="og:description" content="Play multiplayer games with Person Picker. Connect with friends and colleagues remotely for fun selection games.">
    <meta property="og:image" content="https://personpicker.com/android-chrome-512x512.png">

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:url" content="https://personpicker.com/multiplayer.html">
    <meta name="twitter:title" content="Person Picker - Multiplayer Games">
    <meta name="twitter:description" content="Play multiplayer games with Person Picker. Connect with friends and colleagues remotely for fun selection games.">
    <meta name="twitter:image" content="https://personpicker.com/android-chrome-512x512.png">

    <!-- Favicon -->
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">

    <!-- Stylesheets -->
    <link rel="stylesheet" href="/public/css/styles.css">
    <link rel="stylesheet" href="/multiplayer/public/css/multiplayer.css">
    <link rel="stylesheet" href="/public/css/multiplayer-hub.css">

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-CXC0589Q5T"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-CXC0589Q5T');
    </script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Person Picker Multiplayer</h1>
            <p class="subheading">Play together with friends and colleagues from anywhere</p>
            <nav>
                <a href="/welcome.html" class="nav-button">Back to Home</a>
                <a href="/single-player.html" class="nav-button">Switch to Single Player</a>
            </nav>
        </header>

        <div class="connection-status">
            <div id="connectionStatus" class="status-indicator disconnected">Disconnected</div>
        </div>

        <div class="lobby-browser compact">
            <h2>Join Existing Lobbies</h2>

            <div class="lobby-controls-compact">
                <div class="join-form">
                    <input type="text" id="joinPlayerName" placeholder="Your name" class="compact-input">
                    <input type="text" id="directRoomId" placeholder="Room ID" class="compact-input" style="text-transform: uppercase;">
                    <button id="directJoinBtn" class="btn-primary compact" disabled>Join</button>
                </div>

                <div class="lobby-list-compact">
                    <div class="lobby-section-title">Available Lobbies</div>
                    <div class="lobby-header">
                        <button id="refreshLobbiesBtn" class="btn-secondary compact">Refresh</button>
                        <span id="lobbyCount" class="lobby-count">0 lobbies</span>
                    </div>
                    <div id="lobbiesList" class="lobbies-list-compact">
                        <div class="loading-lobbies">Loading...</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="game-selection">
            <h2>Select a Multiplayer Game</h2>

            <div class="game-grid">
                <div class="game-card random-game-card">
                    <div class="game-icon">🎲</div>
                    <h3>Random Game</h3>
                    <p>Let chance decide! Click here to randomly select one of the available multiplayer games.</p>
                    <button id="randomGameButton" class="play-button">Play Random Game</button>
                </div>

                <div class="game-card">
                    <div class="game-icon">🧰</div>
                    <h3>Chance Chest Challenge</h3>
                    <p>Players take turns selecting chests. Once all chests are chosen, one is randomly picked as the winner.</p>
                    <a href="/multiplayer/games/chance-chest-challenge/" class="play-button">Play Now</a>
                </div>

                <div class="game-card">
                    <div class="game-icon">✂️</div>
                    <h3>Rock Paper Scissors Tournament</h3>
                    <p>Battle it out in a tournament-style Rock Paper Scissors game. Choose your move and see who emerges victorious!</p>
                    <a href="/multiplayer/games/rock-paper-scissors-tournament/" class="play-button">Play Now</a>
                </div>

                <div class="game-card">
                    <div class="game-icon">🔍</div>
                    <h3>Grid Quest</h3>
                    <p>Find players on the grid. If you find yourself, you're selected. If you find someone else, they win!</p>
                    <a href="/multiplayer/games/grid-quest/" class="play-button">Play Now</a>
                </div>
            </div>
        </div>

        <div class="how-to-play">
            <h2>How to Play Multiplayer Games</h2>

            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>Select a Game</h3>
                    <p>Choose one of the multiplayer games from the selection above.</p>
                </div>

                <div class="step">
                    <div class="step-number">2</div>
                    <h3>Create a Room</h3>
                    <p>Enter your name and create a new game room.</p>
                </div>

                <div class="step">
                    <div class="step-number">3</div>
                    <h3>Invite Others</h3>
                    <p>Share the room ID or link with friends and colleagues.</p>
                </div>

                <div class="step">
                    <div class="step-number">4</div>
                    <h3>Start the Game</h3>
                    <p>Once everyone has joined, the host can start the game.</p>
                </div>
            </div>
        </div>

        <footer>
            <p>© 2025 Person Picker - Your Trusted Random Name Selector. All rights reserved.</p>
        </footer>
    </div>

    <script src="/multiplayer/public/js/multiplayer-client.js"></script>
    <script src="/public/js/multiplayer-hub.js"></script>

    <style>
        /* Special styling for the random game card */
        .random-game-card {
            background: linear-gradient(135deg, var(--accent-color) 0%, var(--secondary-color) 100%);
            color: white;
            border: 2px solid var(--accent-color);
        }

        .random-game-card h3 {
            color: white;
        }

        .random-game-card p {
            color: rgba(255, 255, 255, 0.9);
        }

        .random-game-card .play-button {
            background-color: rgba(255, 255, 255, 0.2);
            border: 2px solid white;
            color: white;
        }

        .random-game-card .play-button:hover {
            background-color: white;
            color: var(--secondary-color);
        }

        .random-game-card:hover {
            transform: translateY(-10px) scale(1.02);
        }

        /* Lobby Browser Styles */
        .lobby-browser.compact {
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.03);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.08);
        }

        .lobby-browser.compact h2 {
            color: var(--text-color);
            margin-bottom: 1rem;
            text-align: center;
            font-size: 1.3rem;
        }

        .lobby-controls-compact {
            display: flex;
            flex-direction: column;
            gap: 1.5rem;
            align-items: center;
        }

        .join-form {
            display: flex;
            gap: 0.5rem;
            align-items: end;
            justify-content: center;
            width: 100%;
            max-width: 500px;
        }

        .compact-input {
            padding: 0.5rem;
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            background: rgba(255, 255, 255, 0.05);
            color: var(--text-color);
            font-size: 0.9rem;
            flex: 1;
        }

        .compact-input:focus {
            outline: none;
            border-color: var(--accent-color);
            background: rgba(255, 255, 255, 0.08);
        }

        .btn-primary.compact, .btn-secondary.compact {
            padding: 0.5rem 1rem;
            font-size: 0.9rem;
            border-radius: 6px;
        }

        .btn-primary, .btn-secondary {
            border: none;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--accent-color);
            color: white;
        }

        .btn-primary:hover:not(:disabled) {
            background: var(--secondary-color);
            transform: translateY(-2px);
        }

        .btn-primary:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            color: var(--text-color);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }

        .lobby-list-compact {
            background: rgba(255, 255, 255, 0.05);
            border: 2px solid rgba(139, 190, 178, 0.3);
            border-radius: 10px;
            padding: 1rem;
            width: 100%;
            max-width: 600px;
            box-shadow: 0 4px 12px rgba(24, 49, 79, 0.1);
            position: relative;
        }

        .lobby-list-compact::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: linear-gradient(135deg, rgba(139, 190, 178, 0.2) 0%, rgba(56, 78, 119, 0.2) 100%);
            border-radius: 10px;
            z-index: -1;
        }

        .lobby-section-title {
            font-size: 1rem;
            font-weight: 600;
            color: var(--accent-color);
            text-align: center;
            margin-bottom: 0.75rem;
            padding-bottom: 0.5rem;
            border-bottom: 1px solid rgba(139, 190, 178, 0.2);
        }

        .lobby-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.5rem;
        }

        .lobby-count {
            color: var(--text-secondary);
            font-size: 0.8rem;
        }

        .lobbies-list-compact {
            min-height: 60px;
            max-height: 120px;
            overflow-y: auto;
        }

        .loading-lobbies {
            text-align: center;
            color: var(--text-secondary);
            padding: 1rem;
            font-size: 0.9rem;
        }

        .lobby-item {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            padding: 0.5rem;
            margin-bottom: 0.3rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .lobby-item:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: var(--accent-color);
        }

        .lobby-info h4 {
            color: var(--text-color);
            margin: 0 0 0.2rem 0;
            font-size: 0.9rem;
        }

        .lobby-details {
            color: var(--text-secondary);
            font-size: 0.75rem;
        }

        .lobby-join-btn {
            padding: 0.3rem 0.6rem;
            background: var(--accent-color);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 0.8rem;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lobby-join-btn:hover {
            background: var(--secondary-color);
            transform: translateY(-1px);
        }

        .no-lobbies {
            text-align: center;
            color: var(--text-secondary);
            padding: 1rem;
            font-style: italic;
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .lobby-controls-compact {
                gap: 1rem;
            }

            .join-form {
                flex-direction: column;
                gap: 0.5rem;
                max-width: 100%;
            }

            .lobby-list-compact {
                max-width: 100%;
            }
        }
    </style>

    <script>
        // Random game selection functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Define available multiplayer games (only non-coming-soon games)
            const availableGames = [
                {
                    name: 'Chance Chest Challenge',
                    url: '/multiplayer/games/chance-chest-challenge/'
                },
                {
                    name: 'Rock Paper Scissors Tournament',
                    url: '/multiplayer/games/rock-paper-scissors-tournament/'
                },
                {
                    name: 'Grid Quest',
                    url: '/multiplayer/games/grid-quest/'
                }
            ];

            // Add click event to random game button
            const randomGameButton = document.getElementById('randomGameButton');
            if (randomGameButton) {
                randomGameButton.addEventListener('click', function() {
                    // Select a random game from available games
                    const randomIndex = Math.floor(Math.random() * availableGames.length);
                    const selectedGame = availableGames[randomIndex];

                    // Show loading state briefly
                    randomGameButton.textContent = 'Selecting...';
                    randomGameButton.disabled = true;

                    // Brief delay for visual feedback, then redirect
                    setTimeout(() => {
                        window.location.href = selectedGame.url;
                    }, 800);
                });
            }

            // Lobby Browser functionality
            initializeLobbyBrowser();
        });

        // Global variables for lobby browser
        let wsConnection = null;
        let availableRooms = [];

        function initializeLobbyBrowser() {
            const joinPlayerNameInput = document.getElementById('joinPlayerName');
            const directRoomIdInput = document.getElementById('directRoomId');
            const directJoinBtn = document.getElementById('directJoinBtn');
            const refreshLobbiesBtn = document.getElementById('refreshLobbiesBtn');
            const lobbiesList = document.getElementById('lobbiesList');
            const lobbyCount = document.getElementById('lobbyCount');

            // Enable/disable join button based on input fields
            function checkJoinButton() {
                const hasName = joinPlayerNameInput.value.trim().length > 0;
                const hasRoomId = directRoomIdInput.value.trim().length === 6;
                directJoinBtn.disabled = !(hasName && hasRoomId);
            }

            joinPlayerNameInput.addEventListener('input', checkJoinButton);
            directRoomIdInput.addEventListener('input', function() {
                this.value = this.value.toUpperCase();
                checkJoinButton();
            });

            // Direct join button click
            directJoinBtn.addEventListener('click', function() {
                const playerName = joinPlayerNameInput.value.trim();
                const roomId = directRoomIdInput.value.trim();

                if (playerName && roomId) {
                    // Close the lobby browser WebSocket connection before navigating
                    if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                        wsConnection.close();
                    }

                    // Store join info and redirect to the first available game
                    // The game will handle the actual join
                    sessionStorage.setItem('pendingJoin', JSON.stringify({
                        playerName: playerName,
                        roomId: roomId
                    }));

                    // Redirect to grid quest for now (we could make this smarter later)
                    window.location.href = '/multiplayer/games/grid-quest/?join=' + roomId;
                }
            });

            // Refresh lobbies button
            refreshLobbiesBtn.addEventListener('click', loadAvailableLobbies);

            // Initialize WebSocket connection for lobby browsing
            function initializeWebSocket() {
                if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                    return;
                }

                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.hostname}:4000`;

                wsConnection = new WebSocket(wsUrl);

                wsConnection.onopen = function() {
                    console.log('Connected to lobby browser WebSocket');
                    loadAvailableLobbies();
                };

                wsConnection.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    if (data.type === 'rooms_list') {
                        displayAvailableRooms(data.rooms);
                    }
                };

                wsConnection.onerror = function(error) {
                    console.error('WebSocket error:', error);
                };

                wsConnection.onclose = function() {
                    console.log('WebSocket connection closed');
                    setTimeout(initializeWebSocket, 3000); // Reconnect after 3 seconds
                };
            }

            // Load available lobbies
            function loadAvailableLobbies() {
                if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                    wsConnection.send(JSON.stringify({
                        type: 'list_rooms'
                    }));
                } else {
                    initializeWebSocket();
                }
            }

            // Display available rooms
            function displayAvailableRooms(rooms) {
                availableRooms = rooms;
                lobbyCount.textContent = `${rooms.length} ${rooms.length === 1 ? 'lobby' : 'lobbies'} available`;

                if (rooms.length === 0) {
                    lobbiesList.innerHTML = '<div class="no-lobbies">No lobbies available. Create a new game to start playing!</div>';
                    return;
                }

                lobbiesList.innerHTML = rooms.map(room => {
                    const gameNames = {
                        'chance-chest-challenge': 'Chance Chest Challenge',
                        'rock-paper-scissors-tournament': 'Rock Paper Scissors',
                        'grid-quest': 'Grid Quest'
                    };

                    const gameName = gameNames[room.game] || room.game;
                    const timeAgo = getTimeAgo(room.createdAt);

                    return `
                        <div class="lobby-item">
                            <div class="lobby-info">
                                <h4>${gameName} - Room ${room.id}</h4>
                                <div class="lobby-details">Host: ${room.hostName} • ${room.playerCount} player${room.playerCount !== 1 ? 's' : ''} • Created ${timeAgo}</div>
                            </div>
                            <button class="lobby-join-btn" onclick="joinLobby('${room.id}', '${room.game}')">Join</button>
                        </div>
                    `;
                }).join('');
            }

            // Helper function to get time ago
            function getTimeAgo(dateString) {
                const now = new Date();
                const created = new Date(dateString);
                const diffMs = now - created;
                const diffMins = Math.floor(diffMs / 60000);

                if (diffMins < 1) return 'just now';
                if (diffMins < 60) return `${diffMins}m ago`;

                const diffHours = Math.floor(diffMins / 60);
                if (diffHours < 24) return `${diffHours}h ago`;

                const diffDays = Math.floor(diffHours / 24);
                return `${diffDays}d ago`;
            }

            // Initialize
            initializeWebSocket();
        }

        // Global function to join a lobby
        function joinLobby(roomId, gameType) {
            const playerName = document.getElementById('joinPlayerName').value.trim();

            if (!playerName) {
                alert('Please enter your name first');
                document.getElementById('joinPlayerName').focus();
                return;
            }

            // Close the lobby browser WebSocket connection before navigating
            if (wsConnection && wsConnection.readyState === WebSocket.OPEN) {
                wsConnection.close();
            }

            // Store join info and redirect to the appropriate game
            sessionStorage.setItem('pendingJoin', JSON.stringify({
                playerName: playerName,
                roomId: roomId
            }));

            // Redirect to the specific game
            window.location.href = `/multiplayer/games/${gameType}/?join=${roomId}`;
        }
    </script>
</body>
</html>
