/*!
 * Person Picker Multiplayer Chance Chest Challenge (https://personpicker.com)
 * Copyright 2024 - Licensed under AGPLv3 (https://www.gnu.org/licenses/agpl-3.0.en.html)
 */

// Game state
let gameState = {
    players: [],
    selectionOrder: [],
    selectedCases: {},
    currentPlayerIndex: 0,
    gameStarted: false,
    gameEnded: false,
    winner: null,
    winningCaseIndex: null
};

// Client state
let clientState = {
    clientId: null,
    playerName: '',
    selectedEmoji: '👤', // Default emoji
    roomId: null,
    isHost: false,
    isMyTurn: false
};

// Available emojis for selection
const availableEmojis = [
    '😀', '😎', '🤠', '👻', '🐱', '🐶', '🦊', '🐼',
    '🐨', '🦁', '🐯', '🦄', '🐙', '🦋', '🌟'
];

// DOM Elements
const elements = {
    // Sections
    lobbySection: document.getElementById('lobbySection'),
    roomSection: document.getElementById('roomSection'),
    gameSection: document.getElementById('gameSection'),

    // <PERSON><PERSON> controls
    playerNameInput: document.getElementById('playerName'),
    emojiSelector: document.getElementById('emojiSelector'),
    joinEmojiSelector: document.getElementById('joinEmojiSelector'),
    createRoomBtn: document.getElementById('createRoomBtn'),
    joinRoomBtn: document.getElementById('joinRoomBtn'),
    joinRoomForm: document.getElementById('joinRoomForm'),
    roomIdInput: document.getElementById('roomIdInput'),
    confirmJoinBtn: document.getElementById('confirmJoinBtn'),
    cancelJoinBtn: document.getElementById('cancelJoinBtn'),

    // Room controls
    roomIdDisplay: document.getElementById('roomId'),
    copyLinkBtn: document.getElementById('copyLink'),
    playerList: document.getElementById('playerList'),
    hostControls: document.getElementById('hostControls'),
    startGameBtn: document.getElementById('startGameBtn'),
    leaveRoomBtn: document.getElementById('leaveRoomBtn'),

    // Game elements
    turnIndicator: document.getElementById('turnIndicator'),
    teamList: document.getElementById('teamList'),
    briefcases: document.getElementById('briefcases'),
    message: document.getElementById('message'),
    winnerMessage: document.getElementById('winnerMessage'),
    revealButton: document.getElementById('revealButton'),
    newGameBtn: document.getElementById('newGameBtn'),

    // Status
    connectionStatus: document.getElementById('connectionStatus'),
    statusMessage: document.getElementById('statusMessage')
};

// Initialize multiplayer client
const client = new MultiplayerClient({
    debug: true
});

// Initialize the application
function init() {
    // Add event listeners for UI elements after DOM is ready
    if (elements.createRoomBtn) elements.createRoomBtn.addEventListener('click', createRoom);
    if (elements.joinRoomBtn) elements.joinRoomBtn.addEventListener('click', showJoinRoomForm);
    if (elements.confirmJoinBtn) elements.confirmJoinBtn.addEventListener('click', joinRoom);
    if (elements.cancelJoinBtn) elements.cancelJoinBtn.addEventListener('click', hideJoinRoomForm);
    if (elements.copyLinkBtn) elements.copyLinkBtn.addEventListener('click', copyRoomLink);
    if (elements.startGameBtn) elements.startGameBtn.addEventListener('click', startGame);
    if (elements.leaveRoomBtn) elements.leaveRoomBtn.addEventListener('click', leaveRoom);
    if (elements.revealButton) elements.revealButton.addEventListener('click', revealWinner);
    if (elements.newGameBtn) elements.newGameBtn.addEventListener('click', requestNewGame);

    // Try to load player name from localStorage
    const savedName = localStorage.getItem('playerName');
    if (savedName && elements.playerNameInput) {
        elements.playerNameInput.value = savedName;
    }

    // Try to load selected emoji from localStorage
    const savedEmoji = localStorage.getItem('selectedEmoji');
    if (savedEmoji) {
        clientState.selectedEmoji = savedEmoji;
    }

    // Initialize emoji selector
    initEmojiSelector();

    // Connect to the WebSocket server
    connectToServer();
}

// Initialize the emoji selector
function initEmojiSelector() {
    // Initialize main emoji selector
    initSingleEmojiSelector(elements.emojiSelector);

    // Initialize join form emoji selector
    initSingleEmojiSelector(elements.joinEmojiSelector);
}

// Initialize a single emoji selector
function initSingleEmojiSelector(selectorElement) {
    // Clear existing content
    selectorElement.innerHTML = '';

    // Add emoji options
    availableEmojis.forEach(emoji => {
        const emojiElement = document.createElement('div');
        emojiElement.className = 'emoji-option';
        if (emoji === clientState.selectedEmoji) {
            emojiElement.classList.add('selected');
        }
        emojiElement.textContent = emoji;
        emojiElement.addEventListener('click', () => selectEmoji(emoji));
        selectorElement.appendChild(emojiElement);
    });
}

// Handle emoji selection
function selectEmoji(emoji) {
    // Update selected emoji
    clientState.selectedEmoji = emoji;

    // Save to localStorage
    localStorage.setItem('selectedEmoji', emoji);

    // Update both selectors
    updateEmojiSelectorUI(elements.emojiSelector, emoji);
    updateEmojiSelectorUI(elements.joinEmojiSelector, emoji);
}

// Update the UI of an emoji selector
function updateEmojiSelectorUI(selectorElement, selectedEmoji) {
    if (!selectorElement) return;

    const options = selectorElement.querySelectorAll('.emoji-option');
    options.forEach(el => {
        el.classList.remove('selected');
        if (el.textContent === selectedEmoji) {
            el.classList.add('selected');
        }
    });
}

// Connect to the WebSocket server
function connectToServer() {
    updateConnectionStatus('connecting');

    client.connect()
        .then(() => {
            updateConnectionStatus('connected');
        })
        .catch(error => {
            console.error('Failed to connect:', error);
            updateConnectionStatus('disconnected');
            showStatusMessage('Failed to connect to server. Please try again.', 'error');
        });
}

// Create a new game room
function createRoom() {
    const playerName = elements.playerNameInput.value.trim();
    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }

    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;

    // Create room with selected emoji
    client.createRoom('chance-chest-challenge', playerName, clientState.selectedEmoji);
}

// Show the join room form
function showJoinRoomForm() {
    elements.joinRoomForm.classList.remove('hidden');

    // Make sure the emoji selector is initialized with the current selection
    updateEmojiSelectorUI(elements.joinEmojiSelector, clientState.selectedEmoji);
}

// Hide the join room form
function hideJoinRoomForm() {
    elements.joinRoomForm.classList.add('hidden');

    // Restore normal lobby interface if it was hidden for direct joining
    const actionCards = document.querySelector('.action-cards');
    const divider = document.querySelector('.divider');

    if (actionCards) actionCards.style.display = '';
    if (divider) divider.style.display = '';

    // Remove direct join message if it exists
    const directJoinMessage = elements.joinRoomForm.querySelector('.direct-join-message');
    if (directJoinMessage) {
        directJoinMessage.remove();
    }

    // Reset form title
    const formTitle = elements.joinRoomForm.querySelector('h3');
    if (formTitle) {
        formTitle.textContent = 'Join Challenge Room';
    }
}

// Show streamlined interface for direct link joining
function showDirectJoinInterface(roomId) {
    // Hide the normal lobby interface elements
    const actionCards = document.querySelector('.action-cards');
    const divider = document.querySelector('.divider');

    if (actionCards) actionCards.style.display = 'none';
    if (divider) divider.style.display = 'none';

    // Pre-populate room ID and show join form
    elements.roomIdInput.value = roomId.toUpperCase();

    // Update the form title to be more specific
    const formTitle = elements.joinRoomForm.querySelector('h3');
    if (formTitle) {
        formTitle.textContent = `Join Challenge Room: ${roomId.toUpperCase()}`;
    }

    // Add a helpful message
    const formCard = elements.joinRoomForm.querySelector('.form-card');
    if (formCard && !formCard.querySelector('.direct-join-message')) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'direct-join-message';
        messageDiv.style.cssText = `
            background: linear-gradient(135deg, #e8f5e8 0%, #f0f8f0 100%);
            border: 2px solid #4caf50;
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            text-align: center;
            color: #2e7d32;
            font-weight: 500;
        `;
        messageDiv.innerHTML = `
            <div style="font-size: 1.2rem; margin-bottom: 0.5rem;">🎯</div>
            <div>You're joining a specific challenge room!</div>
            <div style="font-size: 0.9rem; margin-top: 0.5rem; opacity: 0.8;">
                Just enter your name, pick an emoji, and join the fun.
            </div>
        `;
        formCard.insertBefore(messageDiv, formCard.firstChild.nextSibling);
    }

    // Show the join form
    elements.joinRoomForm.classList.remove('hidden');

    // Focus on the player name input if it's empty
    if (!elements.playerNameInput.value.trim()) {
        elements.playerNameInput.focus();
    }
}

// Join an existing room
function joinRoom() {
    const playerName = elements.playerNameInput.value.trim();
    const roomId = elements.roomIdInput.value.trim();

    if (!playerName) {
        showStatusMessage('Please enter your name', 'error');
        return;
    }

    if (!roomId) {
        showStatusMessage('Please enter a room ID', 'error');
        return;
    }

    // Save player name to localStorage
    localStorage.setItem('playerName', playerName);
    clientState.playerName = playerName;

    // Join room with selected emoji
    client.joinRoom(roomId, playerName, clientState.selectedEmoji);
}

// Copy room link to clipboard
function copyRoomLink() {
    const roomId = clientState.roomId;
    const url = `${window.location.origin}${window.location.pathname}?roomId=${roomId}`;

    navigator.clipboard.writeText(url)
        .then(() => {
            showStatusMessage('Room link copied to clipboard', 'success');
        })
        .catch(err => {
            console.error('Failed to copy:', err);
            showStatusMessage('Failed to copy link', 'error');
        });
}

// Leave the current room
function leaveRoom() {
    client.leaveRoom();
    showLobby();
}

// Start the game (host only)
function startGame() {
    if (!clientState.isHost) return;

    const players = client.room.players.map(p => p.name);
    if (players.length < 2) {
        showStatusMessage('Need at least 2 players to start', 'error');
        return;
    }

    // Initialize game state
    const selectionOrder = [...players];
    shuffleArray(selectionOrder);

    const initialState = {
        players: players,
        selectionOrder: selectionOrder,
        selectedCases: {},
        currentPlayerIndex: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        winningCaseIndex: null
    };

    // Start the game
    client.startGame(initialState);
}

// Request a new game after the current one ends
function requestNewGame() {
    if (!clientState.isHost) return;

    // Reset game state
    const players = client.room.players.map(p => p.name);
    const selectionOrder = [...players];
    shuffleArray(selectionOrder);

    const newGameState = {
        players: players,
        selectionOrder: selectionOrder,
        selectedCases: {},
        currentPlayerIndex: 0,
        gameStarted: true,
        gameEnded: false,
        winner: null,
        winningCaseIndex: null
    };

    // Send game action to start a new game
    client.sendGameAction('new_game', newGameState);
}

// Create briefcases for the game
function createBriefcases() {
    elements.briefcases.innerHTML = '';

    for (let i = 0; i < gameState.players.length; i++) {
        const briefcase = document.createElement('div');
        briefcase.className = 'briefcase';
        briefcase.textContent = i + 1;

        // Check if this case is already selected
        if (gameState.selectedCases[i]) {
            briefcase.classList.add('selected');
        } else {
            // Only add click handler if it's not selected and it's the player's turn
            briefcase.onclick = () => selectBriefcase(i);
        }

        elements.briefcases.appendChild(briefcase);
    }
}

// Select a briefcase
function selectBriefcase(index) {
    if (!gameState.gameStarted || gameState.gameEnded || !clientState.isMyTurn) return;
    if (gameState.selectedCases[index]) return;

    // Send the selection action
    client.sendGameAction('select_briefcase', {
        playerName: clientState.playerName,
        caseIndex: index
    });
}

// Process briefcase selection
function processBriefcaseSelection(playerName, index) {
    if (!gameState.gameStarted || gameState.gameEnded) return;
    if (gameState.selectedCases[index]) return;

    // Update game state
    gameState.selectedCases[index] = gameState.selectionOrder[gameState.currentPlayerIndex];

    // Update UI
    const briefcase = elements.briefcases.children[index];
    briefcase.classList.add('selected');
    briefcase.onclick = null;

    // Check if all cases are selected
    if (Object.keys(gameState.selectedCases).length === gameState.players.length) {
        updateMessage("All chance chests selected! You can now reveal the winner.");
        if (clientState.isHost) {
            elements.revealButton.disabled = false;
        }
    } else {
        // Move to next player
        gameState.currentPlayerIndex = (gameState.currentPlayerIndex + 1) % gameState.selectionOrder.length;
        updateMessage(`${gameState.selectionOrder[gameState.currentPlayerIndex]}, select a chance chest!`);
    }

    // Update team list to highlight current player
    updateTeamList();

    // Update turn indicator
    updateTurnIndicator();

    // Update game state
    client.sendGameAction('update_state', gameState);
}

// Reveal the winner
function revealWinner() {
    if (!clientState.isHost) return;
    if (!gameState.gameStarted || gameState.gameEnded) return;
    if (Object.keys(gameState.selectedCases).length !== gameState.players.length) return;

    // Randomly select a winning case
    const caseIndices = Object.keys(gameState.selectedCases);
    const winningIndex = caseIndices[Math.floor(Math.random() * caseIndices.length)];
    const winner = gameState.selectedCases[winningIndex];

    // Update game state
    gameState.gameEnded = true;
    gameState.winner = winner;
    gameState.winningCaseIndex = winningIndex;

    // Send game action to reveal winner
    client.sendGameAction('reveal_winner', gameState);
}

// Process winner reveal
function processWinnerReveal() {
    if (!gameState.gameEnded || !gameState.winner) return;

    const winningIndex = gameState.winningCaseIndex;
    const winner = gameState.winner;

    // Update UI to show who selected each case
    const briefcases = elements.briefcases.children;
    for (let i = 0; i < briefcases.length; i++) {
        briefcases[i].textContent = truncateName(gameState.selectedCases[i] || '');
        briefcases[i].style.backgroundColor = 'var(--input-background)';
        briefcases[i].style.color = 'var(--text-color)';
    }

    // Highlight the winning case
    setTimeout(() => {
        briefcases[winningIndex].classList.add('winner');
        briefcases[winningIndex].style.backgroundColor = 'var(--primary-color)';
        briefcases[winningIndex].style.color = 'white';

        // Show confetti
        confetti?.({
            particleCount: 100,
            spread: 70,
            origin: { y: 0.6 }
        });

        // Show winner message
        elements.winnerMessage.textContent = `🎉 ${winner} has uncovered the winning case! 🎉`;
        elements.winnerMessage.classList.add('show');

        updateMessage("Congratulations to our winner!");

        // Show new game button for host
        if (clientState.isHost) {
            elements.newGameBtn.classList.remove('hidden');
        }

        // Disable reveal button
        elements.revealButton.disabled = true;
    }, 1000);
}

// Helper function to truncate long names
function truncateName(name) {
    return name.length > 8 ? name.substring(0, 7) + '.' : name;
}

// Update the team list display
function updateTeamList() {
    elements.teamList.innerHTML = gameState.selectionOrder.map((member, index) =>
        `<span class="team-member${index === gameState.currentPlayerIndex ? ' current' : ''}">${member}</span>`
    ).join('');
}

// Update the message display
function updateMessage(msg) {
    elements.message.textContent = msg;
}

// Update the turn indicator
function updateTurnIndicator() {
    const currentPlayer = gameState.selectionOrder[gameState.currentPlayerIndex];
    clientState.isMyTurn = currentPlayer === clientState.playerName;

    if (gameState.gameEnded) {
        elements.turnIndicator.textContent = `Game over! ${gameState.winner} wins!`;
    } else if (gameState.gameStarted) {
        elements.turnIndicator.textContent = `Current turn: ${currentPlayer}`;
        if (clientState.isMyTurn) {
            elements.turnIndicator.textContent += ' (Your turn!)';
        }
    } else {
        elements.turnIndicator.textContent = 'Waiting for game to start...';
    }
}

// Update the player list
function updatePlayerList() {
    if (!client.room) return;

    const players = client.room.players;
    elements.playerList.innerHTML = '';

    players.forEach(player => {
        const playerItem = document.createElement('div');
        playerItem.className = 'player-item';

        // Create player info container (emoji + name)
        const playerInfo = document.createElement('div');
        playerInfo.className = 'player-info';

        // Add emoji
        const emojiSpan = document.createElement('span');
        emojiSpan.className = 'player-emoji';
        emojiSpan.textContent = player.emoji || '👤'; // Use default if not set
        playerInfo.appendChild(emojiSpan);

        // Add name
        const nameSpan = document.createElement('span');
        nameSpan.className = 'player-name';
        nameSpan.textContent = player.name;

        if (player.id === clientState.clientId) {
            nameSpan.textContent += ' (You)';
        }

        playerInfo.appendChild(nameSpan);

        // Add status (host)
        const statusSpan = document.createElement('span');
        if (player.isHost) {
            statusSpan.className = 'player-host';
            statusSpan.textContent = 'Host';
        }

        playerItem.appendChild(playerInfo);
        playerItem.appendChild(statusSpan);
        elements.playerList.appendChild(playerItem);
    });
}

// Show the lobby section
function showLobby() {
    elements.lobbySection.classList.remove('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the room section
function showRoom() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.remove('hidden');
    elements.gameSection.classList.add('hidden');
}

// Show the game section
function showGame() {
    elements.lobbySection.classList.add('hidden');
    elements.roomSection.classList.add('hidden');
    elements.gameSection.classList.remove('hidden');
}

// Update connection status display
function updateConnectionStatus(status) {
    elements.connectionStatus.className = 'connection-status';

    switch (status) {
        case 'connected':
            elements.connectionStatus.classList.add('connection-connected');
            elements.connectionStatus.textContent = 'Connected';
            break;
        case 'disconnected':
            elements.connectionStatus.classList.add('connection-disconnected');
            elements.connectionStatus.textContent = 'Disconnected';
            break;
        case 'connecting':
            elements.connectionStatus.classList.add('connection-connecting');
            elements.connectionStatus.textContent = 'Connecting...';
            break;
    }
}

// Show a status message
function showStatusMessage(message, type = 'info') {
    elements.statusMessage.textContent = message;
    elements.statusMessage.className = 'status-message fade-in';
    elements.statusMessage.classList.add(`status-${type}`);
    elements.statusMessage.classList.remove('hidden');

    setTimeout(() => {
        elements.statusMessage.classList.add('hidden');
    }, 3000);
}

// Shuffle an array (Fisher-Yates algorithm)
function shuffleArray(array) {
    for (let i = array.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [array[i], array[j]] = [array[j], array[i]];
    }
    return array;
}

// Event handlers for WebSocket events
client.on('connected', (data) => {
    clientState.clientId = data.clientId;

    // Check for pending join from lobby browser
    const pendingJoin = sessionStorage.getItem('pendingJoin');
    if (pendingJoin) {
        try {
            const joinData = JSON.parse(pendingJoin);
            // Clear the pending join data
            sessionStorage.removeItem('pendingJoin');

            // Set player name and join the room automatically
            elements.playerNameInput.value = joinData.playerName;
            clientState.playerName = joinData.playerName;
            localStorage.setItem('playerName', joinData.playerName);

            // Join the room directly
            client.joinRoom(joinData.roomId, joinData.playerName, clientState.selectedEmoji);
            return;
        } catch (error) {
            console.error('Error processing pending join:', error);
            sessionStorage.removeItem('pendingJoin');
        }
    }

    // Check if there's a room ID in the URL (direct link joining)
    const urlParams = new URLSearchParams(window.location.search);
    const roomId = urlParams.get('roomId') || urlParams.get('join');
    if (roomId) {
        // Show streamlined direct join interface
        showDirectJoinInterface(roomId);
        return;
    }
});

client.on('room_created', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = true;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.remove('hidden');
    updatePlayerList();
    showRoom();
});

client.on('room_joined', (data) => {
    clientState.roomId = data.roomId;
    clientState.isHost = false;
    elements.roomIdDisplay.textContent = data.roomId;
    elements.hostControls.classList.add('hidden');
    updatePlayerList();
    showRoom();
});

client.on('player_joined', (data) => {
    updatePlayerList();
    showStatusMessage(`${data.player.name} joined the room`, 'info');
});

client.on('player_left', (data) => {
    updatePlayerList();
    showStatusMessage(`A player left the room`, 'warning');
});

client.on('game_started', (data) => {
    gameState = data.room.gameState;
    createBriefcases();
    updateTeamList();
    updateTurnIndicator();
    updateMessage(`${gameState.selectionOrder[0]}, select a chance chest!`);
    elements.newGameBtn.classList.add('hidden');
    elements.revealButton.disabled = true;
    elements.winnerMessage.textContent = '';
    elements.winnerMessage.classList.remove('show');
    showGame();
});

client.on('game_action', (data) => {
    if (data.action === 'select_briefcase') {
        processBriefcaseSelection(data.playerId, data.gameState.caseIndex);
    } else if (data.action === 'update_state') {
        gameState = data.gameState;
        updateTurnIndicator();
        updateTeamList();
    } else if (data.action === 'reveal_winner') {
        gameState = data.gameState;
        processWinnerReveal();
    } else if (data.action === 'new_game') {
        gameState = data.gameState;
        createBriefcases();
        updateTeamList();
        updateTurnIndicator();
        updateMessage(`${gameState.selectionOrder[0]}, select a chance chest!`);
        elements.revealButton.disabled = true;
        elements.winnerMessage.textContent = '';
        elements.winnerMessage.classList.remove('show');
        elements.newGameBtn.classList.add('hidden');
    }
});

client.on('error', (data) => {
    showStatusMessage(data.message, 'error');
});

client.on('disconnected', () => {
    updateConnectionStatus('disconnected');
    showStatusMessage('Disconnected from server. Trying to reconnect...', 'error');
});

client.on('reconnected', () => {
    updateConnectionStatus('connected');
    showStatusMessage('Reconnected to server', 'success');
});

// Initialize the application when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', init);
} else {
    init();
}
